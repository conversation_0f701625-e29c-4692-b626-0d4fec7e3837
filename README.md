# Yemen IMF Alignment Tool

**Version 2.0 - Identity Preserving Edition**

A sophisticated tool for aligning World Bank macroeconomic data with IMF program targets while preserving economic identities and ensuring data consistency.

## 🎯 Key Features

- **100% IMF Target Achievement**: Successfully aligns 18 out of 18 IMF indicators
- **Identity Preservation**: Maintains 11 out of 13 economic identities (up from 7/13)
- **Real Value Protection**: Never modifies real (constant price) values
- **Comprehensive Validation**: Built-in economic identity validation system
- **Professional Reporting**: Detailed alignment reports with full transparency

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- 2GB RAM minimum
- 500MB disk space

### Installation

```bash
# Clone or download this repository
cd yemen-imf-alignment

# Create virtual environment
python3 -m venv env
source env/bin/activate  # On Windows: env\Scripts\activate

# Install dependencies
pip install -r requirements.txt
```

### Basic Usage

```bash
# Run the alignment
python align_to_imf.py

# Check results
cat outputs/alignment_report.md
```

## 📊 What This Tool Does

### 1. Loads Yemen Macroeconomic Data
- Reads comprehensive time series (1990-2027)
- Handles World Bank MFMOD format
- Preserves data integrity

### 2. Applies IMF Program Targets
- GDP in USD (2022-2025)
- Import values
- Fiscal indicators as % of GDP
- Maintains consistency across all variables

### 3. Preserves Economic Identities
Successfully maintains:
- ✅ GDP expenditure approach (nominal & real)
- ✅ GDP production approach (nominal & real)
- ✅ Deflator relationships
- ✅ Investment decomposition
- ✅ Consumption decomposition
- ✅ Fiscal identities
- ✅ Balance of payments identity
- ✅ GDP-fiscal consistency
- ✅ Trade consistency

### 4. Generates Professional Reports
- Detailed adjustment summary
- Identity validation results
- Target achievement metrics
- Clear success/warning indicators

## 📁 Project Structure

```
yemen-imf-alignment/
├── align_to_imf.py          # Main entry point
├── config/                  # Configuration files
│   ├── imf_targets.yaml    # IMF target values
│   └── adjustment_rules.yaml # Optimization constraints
├── src/                     # Core modules
│   ├── data_handler.py     # Data management
│   ├── target_processor.py # Target processing
│   ├── identity_validator.py # Economic validation
│   └── ...                 # Other modules
├── data/                    # Input data
│   └── yemen_macro_data.csv # Yemen macro dataset
├── outputs/                 # Results
│   ├── adjusted_macro_data.csv # Aligned dataset
│   └── alignment_report.md # Detailed report
└── docs/                    # Documentation
```

## 🔧 Configuration

### IMF Targets (config/imf_targets.yaml)
Modify this file to update IMF target values:
```yaml
gdp_usd:
  2022: 23400  # Million USD
  2023: 27000
  2024: 31600
  2025: 36900
```

### Adjustment Rules (config/adjustment_rules.yaml)
Control optimization behavior:
```yaml
max_adjustment_percent:
  default: 50
  gdp_deflator: 30
  imports: 40
```

## 📈 Recent Improvements (v2.0)

### Investment Decomposition Fix
- Ensures Total Investment = Public + Private
- Automatic recalculation after adjustments

### Fiscal Identity Preservation
- Maintains fiscal balance = revenue - expenditure
- Preserves primary balance relationships

### BOP Identity Correction
- Fixed sign conventions for international standards
- Proper handling of reserves and financial account

### Trade Consistency
- Synchronizes Balance of Payments trade with National Accounts
- Maintains 1:1 ratios when converted to common currency

### Crisis Economy Handling
- Properly documents 2025 financing gap ($2,025M)
- Uses errors & omissions as balancing item

## 🎓 Understanding the Results

### Success Indicators
- ✅ = Target achieved or identity preserved
- ⚠️ = Minor deviation (usually < 5%)
- ❌ = Significant deviation or failure

### Known Acceptable Deviations
1. **Savings-Investment Gap**: 65-127% gaps are normal for crisis economies
2. **External Sector**: Minor discrepancies due to data limitations

### Interpreting Adjustments
- Positive % = Variable increased to meet targets
- Negative % = Variable decreased to meet targets
- Focus on nominal adjustments (real values unchanged)

## 🤝 Support & Contact

**World Bank Yemen Team**
- Technical Lead: Mohammad Al-Akkaoui
- Email: [contact email]
- Project: Yemen Macroeconomic Framework

## 📋 Version History

### v2.0 (*Last updated: August 2025*)
- Identity preservation framework
- BOP sign convention fixes
- Trade consistency synchronization
- Investment and fiscal identity fixes
- Crisis economy documentation

### v1.0 (*Last updated: August 2025*)
- Initial IMF alignment implementation
- Basic validation framework

## 📄 License

This tool is developed for the World Bank Yemen Country Office.
Usage is subject to World Bank policies and procedures.

---

*For detailed technical documentation, see `docs/TECHNICAL_GUIDE.md`*