#!/usr/bin/env python3
"""
Identity-Preserving IMF Alignment Script
Version 2.0 - Maintains all economic identities while aligning to IMF targets

Author: <PERSON>@worldbank.org
Date: August 2025
"""

import os
import sys
import logging
from pathlib import Path
import pandas as pd

# Add src to path
sys.path.insert(0, str(Path(__file__).parent))

from src.data_handler import YemenDataHandler
from src.target_processor import IMFTargetProcessor
from src.identity_preserving_aligner import IdentityPreservingAligner, AlignmentResult

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def main():
    """Main identity-preserving alignment process"""
    
    print("=" * 80)
    print("IDENTITY-PRESERVING IMF ALIGNMENT - Version 2.0")
    print("=" * 80)
    print("\nThis version maintains all economic identities while aligning to IMF targets.")
    print("Key improvement: Only deflators are adjusted, nominal values update automatically.")
    print("-" * 80)
    
    # Configuration
    years = [2022, 2023, 2024, 2025]
    
    # Paths - using absolute paths relative to script location
    script_dir = Path(__file__).parent
    data_path = script_dir / 'data' / 'yemen_macro_data.csv'
    targets_path = script_dir / 'config' / 'imf_targets.yaml'
    rules_path = script_dir / 'config' / 'adjustment_rules.yaml'
    output_path = script_dir / 'outputs' / 'adjusted_macro_data.csv'
    report_path = script_dir / 'outputs' / 'alignment_report.md'
    
    logger.info("Starting identity-preserving WB-IMF alignment process")
    
    # Step 1: Initialize components
    print("\n📦 Step 1: Initializing components...")
    
    data_handler = YemenDataHandler(str(data_path))
    data_handler.load_data()
    
    target_processor = IMFTargetProcessor(str(targets_path), str(rules_path))
    
    # Create the identity-preserving aligner with crisis economy tolerance
    # For crisis economies, allow higher tolerance for identity violations
    crisis_tolerance = 5.0  # 5% tolerance for crisis economy 
    aligner = IdentityPreservingAligner(data_handler, target_processor, tolerance=crisis_tolerance)
    
    # Step 2: Run alignment
    print("\n📊 Step 2: Running identity-preserving alignment...")
    result = aligner.align_to_targets(years)
    
    # Step 3: Display results
    print("\n📈 Step 3: Alignment Results")
    print("-" * 80)
    
    if result.success:
        print("✅ ALIGNMENT SUCCESSFUL")
        print(f"\nTotal adjustments made: {len(result.adjustments)}")
        
        # Show identity validation
        print(f"\nIdentity validation: {sum(result.identity_validation.values())}/{len(result.identity_validation)} passed")
        for identity, passed in result.identity_validation.items():
            status = "✅" if passed else "❌"
            print(f"  {identity}: {status}")
        
        # Show target achievement
        print("\nTarget achievement:")
        for target_type, years_data in result.target_achievement.items():
            print(f"\n  {target_type}:")
            for year, achievement in years_data.items():
                status = "✅" if 95 <= achievement <= 105 else "⚠️"
                print(f"    {year}: {achievement:.1f}% {status}")
        
        # Save adjusted data
        print(f"\n💾 Saving adjusted data to: {output_path}")
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        data_handler.save_data(str(output_path))
        
        # Generate and save report
        print(f"📄 Generating detailed report...")
        report = aligner.generate_alignment_report(result)
        
        with open(report_path, 'w') as f:
            f.write(report)
        print(f"📄 Report saved to: {report_path}")
        
    else:
        print("❌ ALIGNMENT FAILED")
        print("\nErrors encountered:")
        for error in result.error_messages:
            print(f"  - {error}")
        
        print("\nPartial results:")
        print(f"  Adjustments attempted: {len(result.adjustments)}")
        if result.identity_validation:
            failed = [k for k, v in result.identity_validation.items() if not v]
            if failed:
                print(f"  Failed identities: {', '.join(failed)}")
    
    print("\n" + "=" * 80)
    print("ALIGNMENT PROCESS COMPLETE")
    print("=" * 80)
    
    print("\n📚 Key improvements in this version:")
    print("  1. All economic identities are preserved")
    print("  2. Only deflators are adjusted (maintains Nominal = Real × Deflator/100)")
    print("  3. Fiscal identities include resource revenues")
    print("  4. Cascading updates ensure consistency")
    print("  5. Rollback capability if any identity breaks")
    
    print("\n💡 Next steps:")
    print("  1. Review the alignment report for details")
    print("  2. Validate results with economic reasoning")
    print("  3. Use adjusted data for policy analysis")


if __name__ == "__main__":
    # Handle missing pandas import
    try:
        import pandas as pd
    except ImportError:
        print("Error: pandas not found. Please install required packages:")
        print("  pip install -r requirements.txt")
        sys.exit(1)
    
    main()