# Adjustment Rules and Constraints for WB-IMF Alignment

# Priority order for achieving targets (1 = highest)
target_priorities:
  1: gdp_nominal        # Most critical for debt sustainability
  2: fiscal_balance     # IMF program conditionality
  3: government_revenue # Fiscal sustainability
  4: government_expenditure
  5: imports           # Trade financing needs
  6: inflation         # Monetary stability
  7: exports           # Less critical given oil halt

constraints:
  # Preserve real values - do not adjust any KN (constant price) variables
  preserve_real_values: true
  
  # Special preservation rules
  preserve_2022_fiscal_revenue: true  # Keep WB value for 2022 revenue
  
  # Oil exports fixed at WB levels (not IMF assumptions)
  oil_exports_fixed_at_wb: true
  
  # Mathematical consistency
  ensure_nominal_equals_real_times_deflator: true
  
  # Exchange rates already match - no adjustment
  preserve_exchange_rates: true

adjustments_needed:
  gdp_deflator:
    2022: 0.0  # No adjustment (already matches)
    2023: [10.0, 50.0]  # Allow range: 10-20% increase
    2024: [5.0, 50.0]   # Allow range: 5-12% increase
    2025: [15.0, 50.0]  # Allow range: 15-25% increase
  
  export_deflator:
    # Since WB non-oil exports already exceed IMF targets, no adjustment needed
    2022: 0.0
    2023: 0.0
    2024: 0.0
    2025: 0.0
  
  import_deflator:
    2022: 0.0  # No adjustment
    2023: 0.0  # No adjustment
    2024: [10.0, 50.0]  # Allow range: 10-20% increase
    2025: [20.0, 50.0]  # Allow range: 20-30% increase
  
  cpi_adjustment:
    # Adjust CPI to achieve target inflation rates
    2024: true  # To achieve 33.9% inflation
    2025: true  # To achieve 20.4% inflation
  
  fiscal_adjustments:
    revenue_2023: -0.2  # From 6.3% to 6.1% of GDP
    revenue_2024: true  # Adjust to achieve 6.4% of GDP
    revenue_2025: true  # Adjust to achieve 5.9% of GDP
    expenditure_2023: -1.8  # From 13.6% to 11.8% of GDP
    expenditure_2024: true  # Adjust to achieve 8.9% of GDP
    expenditure_2025: -0.2  # From 9.8% to 9.6% of GDP

# Optimization parameters
optimization:
  # Deflator bounds - more flexible for crisis economy
  deflator_bounds:
    min: 0.7   # Allow 30% deflation (was implicit 0.5)
    max: 2.0   # Allow 100% inflation (was implicit 3.0)
  
  # Allow larger statistical discrepancy in crisis
  statistical_discrepancy_tolerance: 0.03  # 3% of GDP (was ~1%)
  
  # Identity tolerance
  identity_tolerance: 0.05  # Allow 5% deviation (was 0.01%)
  
  # Component deflator flexibility
  component_flexibility: 0.20  # Components can deviate 20% from aggregate

# Variables to update in yemen_macro_data.csv
variables_to_update:
  - YEMNYGDPMKTPCN  # Nominal GDP
  - YEMNEIMPGNFSCN  # Nominal imports (2024-2025)
  - YEMGGREVTOTLCN  # Total revenue (2023 only)
  - YEMGGEXPTOTLCN  # Total expenditure (2023, 2025)
  - YEMFPCPITOTLXN  # CPI index (2024 only)
  
# Variables that stay unchanged
variables_unchanged:
  - All KN variables (real values)
  - YEMSPPOPTOTL (population)
  - YEMPANUSATLS (exchange rate)
  - YEMNEEXPGNFSCN (exports - no adjustment needed)