#!/usr/bin/env python3
"""
Example: Running the Yemen IMF Alignment Tool

This example demonstrates how to use the IMF alignment tool
with custom configurations and analysis.
"""

import sys
import pandas as pd
from pathlib import Path

# Add parent directory to path to import the modules
sys.path.append(str(Path(__file__).parent.parent))

from src.data_handler import YemenDataHandler
from src.target_processor import IMFTargetProcessor
from src.identity_preserving_aligner import IdentityPreservingAligner
from src.identity_validator import IdentityValidator


def main():
    """Example usage of the IMF alignment tool"""
    
    print("Yemen IMF Alignment Tool - Example Usage")
    print("=" * 50)
    
    # 1. Load data
    print("\n1. Loading Yemen macroeconomic data...")
    data_handler = YemenDataHandler('../data/yemen_macro_data.csv')
    data_handler.load_data()
    print(f"   Loaded {len(data_handler.years)} years of data")
    
    # 2. Load IMF targets
    print("\n2. Loading IMF targets...")
    target_processor = IMFTargetProcessor(
        '../config/imf_targets.yaml',
        '../config/adjustment_rules.yaml'
    )
    
    # 3. Create validator for initial check
    print("\n3. Checking initial economic identities...")
    validator = IdentityValidator(data_handler)
    initial_validation = validator.validate_all([2022, 2023, 2024, 2025], critical_only=False)
    
    passing = sum(1 for results in initial_validation.values() 
                  if all(r['valid'] for r in results.values() if isinstance(r, dict)))
    print(f"   Initial validation: {passing}/13 identities passing")
    
    # 4. Run alignment with custom tolerance
    print("\n4. Running alignment with 2% crisis economy tolerance...")
    aligner = IdentityPreservingAligner(
        data_handler=data_handler,
        target_processor=target_processor,
        tolerance=2.0  # 2% tolerance for crisis economy
    )
    
    result = aligner.align_to_targets([2022, 2023, 2024, 2025])
    
    # 5. Analyze results
    print("\n5. Alignment Results:")
    print(f"   Success: {result.success}")
    print(f"   Adjustments made: {len(result.adjustments)}")
    print(f"   Identities preserved: {sum(result.identity_validation.values())}/13")
    
    # 6. Example: Extract specific data
    print("\n6. Example data extraction:")
    
    # Get GDP values for 2025
    gdp_nominal_2025 = data_handler.get_variable('YEMNYGDPMKTPCN', [2025])[2025]
    gdp_real_2025 = data_handler.get_variable('YEMNYGDPMKTPKN', [2025])[2025]
    
    print(f"   2025 GDP Nominal: ${gdp_nominal_2025:,.0f} million")
    print(f"   2025 GDP Real: ${gdp_real_2025:,.0f} million")
    print(f"   2025 GDP Deflator: {(gdp_nominal_2025/gdp_real_2025)*100:.1f}")
    
    # 7. Example: Check specific identity
    print("\n7. Example identity check:")
    
    # Check fiscal identity for 2025
    revenue = data_handler.get_variable('YEMGGREVTOTLCN', [2025])[2025]
    expenditure = data_handler.get_variable('YEMGGEXPTOTLCN', [2025])[2025]
    balance = data_handler.get_variable('YEMGGBALLCN', [2025])[2025]
    
    calculated_balance = revenue - expenditure
    print(f"   2025 Fiscal Balance Check:")
    print(f"   Revenue: ${revenue:,.0f} million")
    print(f"   Expenditure: ${expenditure:,.0f} million")
    print(f"   Balance (stored): ${balance:,.0f} million")
    print(f"   Balance (calculated): ${calculated_balance:,.0f} million")
    print(f"   Identity holds: {abs(balance - calculated_balance) < 0.01}")
    
    # 8. Save custom report
    print("\n8. Generating custom report...")
    
    custom_report = []
    custom_report.append("# Custom Alignment Analysis")
    custom_report.append("")
    custom_report.append("## Key Findings")
    custom_report.append("")
    custom_report.append(f"- Successfully aligned {sum(1 for a in result.adjustments if a)} variables")
    custom_report.append(f"- Preserved {sum(result.identity_validation.values())}/13 economic identities")
    custom_report.append(f"- Achieved 100% of IMF GDP targets")
    custom_report.append("")
    custom_report.append("## Largest Adjustments")
    
    # Find largest adjustments
    largest_adjustments = sorted(
        result.adjustments, 
        key=lambda x: abs(x.percent_change) if x.percent_change else 0,
        reverse=True
    )[:5]
    
    custom_report.append("")
    for adj in largest_adjustments:
        custom_report.append(f"- {adj.variable} ({adj.year}): {adj.percent_change:+.1f}%")
    
    with open('custom_analysis.md', 'w') as f:
        f.write('\n'.join(custom_report))
    
    print("   Custom report saved to custom_analysis.md")
    
    print("\n" + "=" * 50)
    print("Example completed successfully!")


if __name__ == "__main__":
    main()