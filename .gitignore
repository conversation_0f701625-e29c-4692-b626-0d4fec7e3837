# Python
__pycache__/
*.py[cod]
*$py.class
*.so

# Virtual Environment
env/
venv/
ENV/
VENV/

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Environments
.env
.venv

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log

# Data files (optional - uncomment if you don't want to track large data files)
# *.xls
# *.xlsx
# *.csv

# Output files (optional - uncomment if you don't want to track outputs)
# outputs/

# Temporary data files
data/temp/*
!data/temp/.gitkeep

# Archive directory
archive/

# Mac specific
.DS_Store