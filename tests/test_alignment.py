#!/usr/bin/env python3
"""
Basic tests for the Yemen IMF Alignment Tool

Run with: python -m pytest tests/test_alignment.py
"""

import sys
import pytest
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from src.data_handler import YemenDataHandler
from src.target_processor import IMFTargetProcessor
from src.identity_validator import IdentityValidator
from src.identity_preserving_aligner import IdentityPreservingAligner


class TestDataHandler:
    """Test data handler functionality"""
    
    def test_load_data(self):
        """Test data loading"""
        handler = YemenDataHandler('data/yemen_macro_data.csv')
        df = handler.load_data()
        
        assert df is not None
        assert len(handler.years) > 0
        assert 2022 in handler.years
        assert 2025 in handler.years
    
    def test_get_variable(self):
        """Test variable retrieval"""
        handler = YemenDataHandler('data/yemen_macro_data.csv')
        handler.load_data()
        
        gdp = handler.get_variable('YEMNYGDPMKTPCN', [2022])
        assert 2022 in gdp.index
        assert gdp[2022] > 0
    
    def test_update_variable(self):
        """Test variable update"""
        handler = YemenDataHandler('data/yemen_macro_data.csv')
        handler.load_data()
        
        # Get original value
        original = handler.get_variable('YEMNYGDPMKTPCN', [2022])[2022]
        
        # Update value
        new_value = original * 1.1
        success = handler.update_variable('YEMNYGDPMKTPCN', 2022, new_value)
        assert success
        
        # Verify update
        updated = handler.get_variable('YEMNYGDPMKTPCN', [2022])[2022]
        assert abs(updated - new_value) < 0.01


class TestTargetProcessor:
    """Test target processor functionality"""
    
    def test_load_targets(self):
        """Test target loading"""
        processor = IMFTargetProcessor(
            'config/imf_targets.yaml',
            'config/adjustment_rules.yaml'
        )
        
        # Test GDP targets
        gdp_targets = processor.get_gdp_targets([2022, 2023, 2024, 2025])
        assert 2022 in gdp_targets
        assert gdp_targets[2022] > 0
    
    def test_fiscal_targets(self):
        """Test fiscal target retrieval"""
        processor = IMFTargetProcessor(
            'config/imf_targets.yaml',
            'config/adjustment_rules.yaml'
        )
        
        fiscal = processor.get_fiscal_targets([2023, 2024, 2025])
        assert 'revenue' in fiscal
        assert 'expenditure' in fiscal


class TestIdentityValidator:
    """Test identity validation functionality"""
    
    def test_gdp_identity(self):
        """Test GDP expenditure identity validation"""
        handler = YemenDataHandler('data/yemen_macro_data.csv')
        handler.load_data()
        
        validator = IdentityValidator(handler)
        
        # Test GDP expenditure identity
        result = validator.validate_gdp_expenditure_nominal(2022)
        assert 'valid' in result
        assert 'gdp_reported' in result
        assert 'gdp_calculated' in result
    
    def test_all_identities(self):
        """Test validation of all identities"""
        handler = YemenDataHandler('data/yemen_macro_data.csv')
        handler.load_data()
        
        validator = IdentityValidator(handler)
        results = validator.validate_all([2022], critical_only=False)
        
        # Should have results for all identity types
        assert len(results) > 0
        assert 'gdp_expenditure_nominal' in results


class TestAlignment:
    """Test the alignment process"""
    
    def test_alignment_success(self):
        """Test successful alignment"""
        # Load components
        handler = YemenDataHandler('data/yemen_macro_data.csv')
        handler.load_data()
        
        processor = IMFTargetProcessor(
            'config/imf_targets.yaml',
            'config/adjustment_rules.yaml'
        )
        
        # Create aligner with high tolerance for testing
        aligner = IdentityPreservingAligner(
            data_handler=handler,
            target_processor=processor,
            tolerance=5.0  # High tolerance for testing
        )
        
        # Run alignment for one year
        result = aligner.align_to_targets([2022])
        
        assert result is not None
        assert hasattr(result, 'success')
        assert hasattr(result, 'adjustments')
        assert hasattr(result, 'identity_validation')
    
    def test_target_achievement(self):
        """Test that targets are achieved"""
        handler = YemenDataHandler('data/yemen_macro_data.csv')
        handler.load_data()
        
        processor = IMFTargetProcessor(
            'config/imf_targets.yaml',
            'config/adjustment_rules.yaml'
        )
        
        aligner = IdentityPreservingAligner(
            data_handler=handler,
            target_processor=processor,
            tolerance=5.0
        )
        
        # Run alignment
        result = aligner.align_to_targets([2022])
        
        if result.success:
            # Check GDP target achievement
            assert 'gdp_usd' in result.target_achievement
            if 2022 in result.target_achievement['gdp_usd']:
                achievement = result.target_achievement['gdp_usd'][2022]
                assert 95 <= achievement <= 105  # Within 5% of target


if __name__ == "__main__":
    # Run basic tests
    print("Running basic tests...")
    
    # Test data loading
    handler = YemenDataHandler('data/yemen_macro_data.csv')
    handler.load_data()
    print(f"✓ Data loaded: {len(handler.years)} years")
    
    # Test target loading
    processor = IMFTargetProcessor(
        'config/imf_targets.yaml',
        'config/adjustment_rules.yaml'
    )
    gdp_targets = processor.get_gdp_targets([2022])
    print(f"✓ Targets loaded: GDP 2022 = ${gdp_targets[2022]:,.0f}M")
    
    # Test validation
    validator = IdentityValidator(handler)
    validation = validator.validate_all([2022], critical_only=False)
    passing = sum(1 for results in validation.values() 
                  if all(r['valid'] for r in results.values() if isinstance(r, dict)))
    print(f"✓ Validation working: {passing}/13 identities pass")
    
    print("\nAll basic tests passed!")
    print("For full test suite, run: python -m pytest tests/test_alignment.py")