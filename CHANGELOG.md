# Changelog

All notable changes to the Yemen IMF Alignment Tool will be documented in this file.

## [2.0.0] - August 2025

### Added
- Identity-preserving alignment framework
- BOP trade synchronization with National Accounts
- Crisis economy tolerance (2% for identity violations)
- Comprehensive validation reporting
- Professional markdown reports
- Example outputs in `examples/` directory

### Fixed
- Investment decomposition identity (Total = Public + Private)
- Fiscal balance identities (Balance = Revenue - Expenditure)
- BOP identity sign conventions (FA and reserves have opposite signs)
- 2025 financing gap handling (uses E&O as balancing item)
- Trade consistency between NA and BOP data

### Changed
- Only deflators are adjusted, never real values
- Improved error handling and rollback capability
- Enhanced logging for transparency
- Better handling of missing data ("NAN" strings)

### Technical Improvements
- Modular architecture with clear separation of concerns
- YAML-based configuration for easy updates
- Robust optimization with fallback strategies
- Comprehensive test coverage

## [1.0.0] - August 2025

### Initial Release
- Basic IMF alignment functionality
- GDP and import targeting
- Fiscal indicator alignment
- Simple validation framework

---

*For questions or support, <NAME_EMAIL>*