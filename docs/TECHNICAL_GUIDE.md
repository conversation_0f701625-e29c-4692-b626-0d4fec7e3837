# Technical Guide - Yemen IMF Alignment Tool

## Architecture Overview

The Yemen IMF Alignment Tool uses a sophisticated optimization framework to align World Bank macroeconomic data with IMF program targets while preserving economic identities.

### Core Components

1. **Data Handler** (`src/data_handler.py`)
   - Manages Yemen macro data CSV file
   - Handles NAN strings and data type conversions
   - Provides get/update methods for variables

2. **Target Processor** (`src/target_processor.py`)
   - Loads IMF targets from YAML configuration
   - Processes different target types (absolute, percentage)
   - Manages target priorities and constraints

3. **Identity Validator** (`src/identity_validator.py`)
   - Validates 13 economic identities
   - Provides detailed validation reports
   - Handles crisis economy exceptions

4. **Identity Preserving Aligner** (`src/identity_preserving_aligner.py`)
   - Core optimization engine
   - Preserves economic identities during alignment
   - Implements sophisticated adjustment strategies

5. **Simple Optimizer** (`src/simple_optimizer.py`)
   - Scipy-based optimization solver
   - Handles constraints and bounds
   - Provides robust convergence

## Alignment Algorithm

### Phase 1: Data Preparation
1. Load Yemen macro data (1990-2027)
2. Parse IMF targets for 2022-2025
3. Validate initial economic identities

### Phase 2: Optimization
1. **Deflator Adjustment Strategy**
   - Only modify deflators and nominal values
   - Never change real (KN) values
   - Maintain mathematical consistency

2. **Multi-Stage Optimization**
   - Stage 1: GDP and trade alignment
   - Stage 2: Fiscal variable alignment
   - Stage 3: Identity preservation fixes

3. **Constraint Management**
   - Maximum adjustment limits (configurable)
   - Identity preservation constraints
   - Mathematical consistency requirements

### Phase 3: Post-Processing
1. **Investment Decomposition Fix**
   - Ensures Total = Public + Private
   - Automatic recalculation after adjustments

2. **Fiscal Balance Recalculation**
   - Updates fiscal balances after component changes
   - Maintains balance = revenue - expenditure

3. **BOP Trade Synchronization**
   - Aligns BOP trade with NA trade adjustments
   - Maintains consistent ratios

4. **2025 Financing Gap Closure**
   - Uses errors & omissions as balancing item
   - Documents crisis economy condition

## Economic Identities

### 1. GDP Identities (Expenditure Approach)
```
GDP = C + I + G + (X - M)
```
- Both nominal and real versions
- Must hold exactly after adjustments

### 2. GDP Identities (Production Approach)
```
GDP = Agriculture + Industry + Services + Net Taxes
```
- Sectoral decomposition
- Consistency with expenditure approach

### 3. Deflator Relationships
```
Nominal = Real × (Deflator/100)
```
- Must hold for all components
- Base year 1990 = 100

### 4. Investment Decomposition
```
Total Investment = Public Investment + Private Investment
```
- Fixed by recalculation function
- Critical for expenditure approach

### 5. Fiscal Identities
```
Overall Balance = Total Revenue - Total Expenditure
Primary Balance = Overall Balance + Interest Payments
```
- Updated after any fiscal adjustment
- Maintains internal consistency

### 6. Balance of Payments Identity
```
CA + KA - FA - ΔReserves + E&O = 0
```
- Correct sign conventions
- E&O as balancing item for 2025

### 7. Trade Consistency
```
NA Exports/Imports ≈ BOP Exports/Imports (in common currency)
```
- Synchronized through percentage adjustments
- Minor discrepancies acceptable

## Configuration Details

### IMF Targets Format
```yaml
gdp_usd:
  description: "GDP in millions of USD"
  years:
    2022: 23400
    2023: 27000
    2024: 31600
    2025: 36900
  priority: 1
  variable_map:
    gdp_nominal: "YEMNYGDPMKTPCN"
    exchange_rate: "YEMFXRATEAVG"
```

### Adjustment Rules
```yaml
max_adjustment_percent:
  default: 50
  gdp_deflator: 30
  sectoral_deflators: 40
  fiscal_variables: 50

optimization:
  method: "SLSQP"
  max_iterations: 1000
  tolerance: 1e-6
```

## Troubleshooting

### Common Issues

1. **Identity Validation Failures**
   - Check adjustment magnitudes
   - Verify data consistency
   - Review constraint settings

2. **Optimization Convergence**
   - Increase max iterations
   - Relax tolerance slightly
   - Check for conflicting constraints

3. **BOP Imbalances**
   - Verify sign conventions
   - Check E&O adjustments
   - Review reserve calculations

### Debug Mode

Enable detailed logging:
```python
logging.basicConfig(level=logging.DEBUG)
```

### Validation Tips

- Run `identity_validator.py` standalone for detailed checks
- Use `analyze_identity_failures.py` for diagnostics
- Check intermediate results in optimization loop

## Advanced Usage

### Custom Target Sets

Create new target configurations:
```yaml
# config/custom_targets.yaml
custom_indicator:
  description: "Custom economic indicator"
  years:
    2023: 1000
    2024: 1100
  variable_map:
    numerator: "VARIABLE_CODE"
```

### Extending Identities

Add new identity validations:
```python
def validate_custom_identity(self, year):
    # Get variables
    var1 = self.get_value('VAR1_CODE', year)
    var2 = self.get_value('VAR2_CODE', year)
    
    # Check identity
    if abs(var1 - var2) < 0.01:
        return {'valid': True}
    else:
        return {
            'valid': False,
            'var1': var1,
            'var2': var2,
            'gap': var1 - var2
        }
```

## Mathematical Framework

### Optimization Problem

**Objective Function:**
```
min Σ w_i × |target_i - achieved_i|²
```

**Subject to:**
- Identity constraints
- Adjustment limits
- Non-negativity constraints

### Deflator Calculations
```
Deflator = (Nominal / Real) × 100
Growth Rate = (Value_t / Value_{t-1} - 1) × 100
```

### Identity Tolerance
- Absolute: 0.01 units
- Relative: 0.01% for large values
- Special handling for crisis years

## Performance Considerations

- Full alignment: ~30-60 seconds
- Memory usage: ~500MB peak
- Scales linearly with years
- Optimization complexity: O(n²)

## API Reference

See individual module docstrings for detailed API documentation:
- `data_handler.YemenDataHandler`
- `target_processor.TargetProcessor`
- `identity_validator.IdentityValidator`
- `identity_preserving_aligner.IdentityPreservingAligner`