"""
Data handler for Yemen macro data
Handles reading and writing the yemen_macro_data.csv file
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class YemenDataHandler:
    """Handler for Yemen macroeconomic data file"""
    
    def __init__(self, data_path: str = 'data/yemen_macro_data.csv'):
        """Initialize data handler
        
        Args:
            data_path: Path to yemen_macro_data.csv
        """
        self.data_path = data_path
        self.df = None
        self.years = None
        self.year_columns = {}
        
    def load_data(self) -> pd.DataFrame:
        """Load Yemen macro data and extract years"""
        logger.info(f"Loading data from {self.data_path}")
        
        self.df = pd.read_csv(self.data_path)
        
        # Extract years from row 0 (assuming structure from original file)
        year_row = self.df.iloc[0, 2:].values  # Years start from column 2
        self.years = []
        
        for i, year in enumerate(year_row):
            try:
                if pd.notna(year) and str(year).strip() != '':
                    year_int = int(float(str(year)))
                    if 1990 <= year_int <= 2030:
                        self.years.append(year_int)
                        self.year_columns[year_int] = i + 2  # Column index
            except (ValueError, TypeError):
                continue
        
        logger.info(f"Loaded data with {len(self.years)} years: {min(self.years)}-{max(self.years)}")
        return self.df
    
    def get_variable(self, var_code: str, years: Optional[List[int]] = None) -> pd.Series:
        """Get values for a specific variable
        
        Args:
            var_code: Variable code (e.g., 'YEMNYGDPMKTPCN')
            years: List of years to extract (default: all)
            
        Returns:
            Series with year as index and values
        """
        if self.df is None:
            self.load_data()
            
        # Find the row with this variable code
        var_row = self.df[self.df.iloc[:, 0] == var_code]
        
        if var_row.empty:
            logger.warning(f"Variable {var_code} not found")
            return pd.Series(dtype=float)
        
        # Extract values for requested years
        if years is None:
            years = self.years
            
        values = {}
        for year in years:
            if year in self.year_columns:
                col_idx = self.year_columns[year]
                value = var_row.iloc[0, col_idx]
                
                # Handle NAN strings
                if str(value).upper() in ['NAN', 'NA', '']:
                    values[year] = np.nan
                else:
                    try:
                        values[year] = float(value)
                    except:
                        values[year] = np.nan
        
        return pd.Series(values)
    
    def update_variable(self, var_code: str, year: int, new_value: float) -> bool:
        """Update a single value in the dataset
        
        Args:
            var_code: Variable code
            year: Year to update
            new_value: New value to set
            
        Returns:
            True if successful
        """
        if self.df is None:
            self.load_data()
            
        # Find the row
        var_rows = self.df[self.df.iloc[:, 0] == var_code]
        if var_rows.empty:
            logger.error(f"Variable {var_code} not found")
            return False
            
        row_idx = var_rows.index[0]
        
        # Find the column
        if year not in self.year_columns:
            logger.error(f"Year {year} not found")
            return False
            
        col_idx = self.year_columns[year]
        
        # Update the value
        self.df.iloc[row_idx, col_idx] = new_value
        logger.debug(f"Updated {var_code}[{year}] = {new_value}")
        
        return True
    
    def update_multiple(self, updates: Dict[str, Dict[int, float]]) -> bool:
        """Update multiple variables and years
        
        Args:
            updates: Dict of {var_code: {year: value}}
            
        Returns:
            True if all updates successful
        """
        success = True
        
        for var_code, year_values in updates.items():
            for year, value in year_values.items():
                if not self.update_variable(var_code, year, value):
                    success = False
                    
        return success
    
    def save_data(self, output_path: Optional[str] = None) -> bool:
        """Save the modified data
        
        Args:
            output_path: Path to save (default: overwrite original)
            
        Returns:
            True if successful
        """
        if output_path is None:
            output_path = self.data_path
            
        try:
            self.df.to_csv(output_path, index=False)
            logger.info(f"Saved data to {output_path}")
            return True
        except Exception as e:
            logger.error(f"Failed to save data: {e}")
            return False
    
    def get_gdp_deflator(self, year: int) -> float:
        """Calculate GDP deflator for a year
        
        Args:
            year: Year to calculate
            
        Returns:
            GDP deflator value
        """
        gdp_real = self.get_variable('YEMNYGDPMKTPKN', [year])[year]
        gdp_nominal = self.get_variable('YEMNYGDPMKTPCN', [year])[year]
        
        if pd.isna(gdp_real) or pd.isna(gdp_nominal) or gdp_real == 0:
            return np.nan
            
        return (gdp_nominal / gdp_real) * 100
    
    def calculate_fiscal_percent_gdp(self, var_code: str, year: int) -> float:
        """Calculate a fiscal variable as percent of GDP
        
        Args:
            var_code: Fiscal variable code
            year: Year to calculate
            
        Returns:
            Value as percent of GDP
        """
        fiscal_value = self.get_variable(var_code, [year])[year]
        gdp_nominal = self.get_variable('YEMNYGDPMKTPCN', [year])[year]
        
        if pd.isna(fiscal_value) or pd.isna(gdp_nominal) or gdp_nominal == 0:
            return np.nan
            
        return (fiscal_value / gdp_nominal) * 100