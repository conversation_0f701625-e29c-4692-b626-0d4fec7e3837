#!/usr/bin/env python3
"""
Simple optimization solver for IMF alignment that properly handles statistical discrepancy
"""

import numpy as np
from scipy.optimize import minimize
import pandas as pd
from typing import Dict, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class SimpleOptimizer:
    """
    Simple optimization solver that finds component deflator adjustments
    to meet IMF targets while preserving identities and keeping statistical
    discrepancy fixed.
    """
    
    def __init__(self, data_handler):
        self.data = data_handler
        
    def solve_for_year(self, year: int, target_gdp_nominal: float, 
                       target_imports_nominal: Optional[float] = None) -> Dict[str, float]:
        """
        Solve for deflator adjustments for a given year
        
        Args:
            year: Year to solve for
            target_gdp_nominal: Target GDP in nominal LCU
            target_imports_nominal: Target imports in nominal LCU (optional)
            
        Returns:
            Dictionary of component -> percent deflator change
        """
        # Define adjustable components (excluding stat disc)
        # Note: We don't include total investment as it's derived from components
        components = {
            'c_private': ('YEMNECONPRVTKN', 'YEMNECONPRVTCN'),
            'c_government': ('YEMNECONGOVTKN', 'YEMNECONGOVTCN'),
            'i_government': ('YEMNEGDIFGOVKN', 'YEMNEGDIFGOVCN'),
            'i_private': ('YEMNEGDIFPRVKN', 'YEMNEGDIFPRVCN'),
            'inventory': ('YEMNEGDISTKBKN', 'YEMNEGDISTKBCN'),
            'exports': ('YEMNEEXPGNFSKN', 'YEMNEEXPGNFSCN'),
            'imports': ('YEMNEIMPGNFSKN', 'YEMNEIMPGNFSCN'),
        }
        
        # Also need production components for consistency
        prod_components = {
            'agriculture': ('YEMNVAGRTOTLKN', 'YEMNVAGRTOTLCN'),
            'industry': ('YEMNVINDTOTLKN', 'YEMNVINDTOTLCN'),
            'services': ('YEMNVSRVTOTLKN', 'YEMNVSRVTOTLCN'),
            'net_taxes': ('YEMNYTAXNINDKN', 'YEMNYTAXNINDCN'),
        }
        
        # Combine all components
        all_components = {**components, **prod_components}
        
        # Get current values
        current_values = {}
        for name, (real_var, nominal_var) in all_components.items():
            real_val = self.data.get_variable(real_var, [year])[year]
            nominal_val = self.data.get_variable(nominal_var, [year])[year]
            if pd.notna(real_val) and real_val != 0:
                current_values[name] = {
                    'real': real_val,
                    'nominal': nominal_val,
                    'deflator': (nominal_val / real_val) * 100
                }
        
        # Get fixed statistical discrepancy
        stat_disc_nominal = self.data.get_variable('YEMNYGDPDISCCN', [year])[year]
        if pd.isna(stat_disc_nominal):
            stat_disc_nominal = 0
            
        # If no import target, use simple proportional adjustment
        if target_imports_nominal is None:
            # Calculate uniform adjustment factor for expenditure components
            current_gdp = sum(v['nominal'] for k, v in current_values.items() 
                            if k in components and k != 'imports')
            current_gdp -= current_values.get('imports', {}).get('nominal', 0)
            current_gdp += stat_disc_nominal
            
            adjustment_factor = (target_gdp_nominal - stat_disc_nominal) / (current_gdp - stat_disc_nominal)
            pct_change = (adjustment_factor - 1) * 100
            
            # Apply uniform change to expenditure components
            deflator_changes = {}
            for name in components:
                if name in current_values:
                    deflator_changes[name] = pct_change
            
            # Now adjust production components to match
            # Calculate current production GDP
            current_prod_gdp = sum(v['nominal'] for k, v in current_values.items() 
                                 if k in prod_components)
            
            # Production GDP should equal expenditure GDP
            prod_adjustment_factor = target_gdp_nominal / current_prod_gdp
            prod_pct_change = (prod_adjustment_factor - 1) * 100
            
            # Apply uniform change to production components
            for name in prod_components:
                if name in current_values:
                    deflator_changes[name] = prod_pct_change
                    
            return deflator_changes
            
        # Otherwise, solve optimization problem
        component_names = list(all_components.keys())
        n_vars = len(component_names)
        
        # Initial guess: no change
        x0 = np.ones(n_vars)
        
        # Bounds: ±50% change
        bounds = [(0.5, 1.5) for _ in range(n_vars)]
        
        # Objective: minimize total deflator changes
        def objective(x):
            total_change = 0
            for i, name in enumerate(component_names):
                # Higher weight for key components
                weight = 2.0 if name in ['c_private', 'i_government', 'i_private'] else 1.0
                total_change += weight * abs(x[i] - 1.0)
            return total_change
        
        # GDP constraint
        def gdp_constraint(x):
            gdp = stat_disc_nominal  # Start with fixed stat disc
            
            # Add expenditure components
            for i, name in enumerate(component_names):
                if name in components and name in current_values:
                    real_val = current_values[name]['real']
                    current_deflator = current_values[name]['deflator']
                    new_deflator = current_deflator * x[i]
                    new_nominal = real_val * new_deflator / 100
                    
                    if name == 'imports':
                        gdp -= new_nominal
                    else:
                        gdp += new_nominal
            
            return gdp - target_gdp_nominal
        
        # Import constraint
        def import_constraint(x):
            imports_idx = component_names.index('imports')
            if 'imports' in current_values:
                real_imports = current_values['imports']['real']
                current_deflator = current_values['imports']['deflator']
                new_deflator = current_deflator * x[imports_idx]
                new_imports = real_imports * new_deflator / 100
                return new_imports - target_imports_nominal
            return 0
        
        # Production-expenditure consistency constraint
        def consistency_constraint(x):
            # GDP from expenditure
            gdp_exp = stat_disc_nominal
            for i, name in enumerate(component_names):
                if name in components and name in current_values:
                    real_val = current_values[name]['real']
                    current_deflator = current_values[name]['deflator']
                    new_deflator = current_deflator * x[i]
                    new_nominal = real_val * new_deflator / 100
                    
                    if name == 'imports':
                        gdp_exp -= new_nominal
                    else:
                        gdp_exp += new_nominal
            
            # GDP from production
            gdp_prod = 0
            for i, name in enumerate(component_names):
                if name in prod_components and name in current_values:
                    real_val = current_values[name]['real']
                    current_deflator = current_values[name]['deflator']
                    new_deflator = current_deflator * x[i]
                    new_nominal = real_val * new_deflator / 100
                    gdp_prod += new_nominal
                    
            return gdp_exp - gdp_prod
        
        constraints = [
            {'type': 'eq', 'fun': gdp_constraint},
            {'type': 'eq', 'fun': import_constraint},
            {'type': 'eq', 'fun': consistency_constraint}
        ]
        
        # Solve
        result = minimize(objective, x0, method='SLSQP', bounds=bounds, 
                         constraints=constraints, options={'maxiter': 1000})
        
        if result.success:
            # Extract deflator changes
            deflator_changes = {}
            for i, name in enumerate(component_names):
                if name in current_values:
                    pct_change = (result.x[i] - 1.0) * 100
                    if abs(pct_change) > 0.001:  # Ignore tiny changes
                        deflator_changes[name] = pct_change
                        
            return deflator_changes
        else:
            logger.warning(f"Optimization failed: {result.message}")
            # Fall back to simpler approach
            # First try without import constraint
            if target_imports_nominal is not None:
                logger.info("Retrying without import constraint...")
                return self.solve_for_year(year, target_gdp_nominal, None)
            else:
                # If still failing, use proportional adjustment
                logger.warning("Using proportional adjustment fallback")
                
                # Calculate adjustment needed
                current_gdp = sum(v['nominal'] for k, v in current_values.items() 
                                if k in components and k != 'imports')
                current_gdp -= current_values.get('imports', {}).get('nominal', 0)
                current_gdp += stat_disc_nominal
                
                adjustment_factor = (target_gdp_nominal - stat_disc_nominal) / (current_gdp - stat_disc_nominal)
                pct_change = (adjustment_factor - 1) * 100
                
                # Apply to all components
                deflator_changes = {}
                for name in all_components:
                    if name in current_values:
                        deflator_changes[name] = pct_change
                        
                return deflator_changes