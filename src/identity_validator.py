#!/usr/bin/env python3
"""
Identity Validator for MFMOD Dataset
Ensures all economic identities and accounting relationships are maintained
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class IdentityValidator:
    """Validates economic identities in the MFMOD dataset"""
    
    def __init__(self, data_handler, tolerance: float = 0.05):
        """
        Initialize validator
        
        Args:
            data_handler: YemenDataHandler instance with loaded data
            tolerance: Maximum acceptable error percentage (default 0.05% for crisis economy)
        """
        self.handler = data_handler
        self.tolerance = tolerance
        self.validation_results = []
        
    def validate_all(self, years: List[int], critical_only: bool = False) -> Dict[str, bool]:
        """
        Run all identity validations
        
        Args:
            years: List of years to validate
            critical_only: If True, only validate critical identities (exclude BOP, S-I)
        
        Returns:
            Dictionary of identity names and pass/fail status
        """
        results = {}
        
        # Clear previous results
        self.validation_results = []
        
        # 1. GDP Identities (CRITICAL)
        results['gdp_expenditure_nominal'] = self.validate_gdp_expenditure_identity(years, real=False)
        results['gdp_expenditure_real'] = self.validate_gdp_expenditure_identity(years, real=True)
        results['gdp_production_nominal'] = self.validate_gdp_production_identity(years, real=False)
        results['gdp_production_real'] = self.validate_gdp_production_identity(years, real=True)
        
        # 2. Component Relationships (CRITICAL)
        results['deflator_relationships'] = self.validate_deflator_relationships(years)
        results['investment_decomposition'] = self.validate_investment_decomposition(years)
        results['consumption_decomposition'] = self.validate_consumption_decomposition(years)
        
        # 3. Fiscal Identities (CRITICAL)
        results['fiscal_identities'] = self.validate_fiscal_identities(years)
        
        # 4. External Sector (CRITICAL for trade consistency)
        results['external_sector'] = self.validate_external_sector_identities(years)
        
        if not critical_only:
            # These have known issues in the initial data
            results['bop_identity'] = self.validate_bop_identity(years)
            results['savings_investment'] = self.validate_savings_investment_identity(years)
        
        # 6. Cross-sector Consistency (CRITICAL)
        results['gdp_fiscal_consistency'] = self.validate_gdp_fiscal_consistency(years)
        results['trade_consistency'] = self.validate_cross_sector_trade_consistency(years)
        
        return results
    
    def validate_gdp_expenditure_identity(self, years: List[int], real: bool = False) -> bool:
        """
        Validate GDP = C + I + G + (X - M) + Statistical Discrepancy
        
        Args:
            years: List of years to validate
            real: If True, check real values (KN), else nominal (CN)
            
        Returns:
            True if identity holds within tolerance for all years
        """
        suffix = 'KN' if real else 'CN'
        identity_name = f"GDP Expenditure ({'Real' if real else 'Nominal'})"
        
        all_valid = True
        
        for year in years:
            # Get components
            gdp = self.handler.get_variable(f'YEMNYGDPMKTP{suffix}', [year]).get(year, 0)
            c_priv = self.handler.get_variable(f'YEMNECONPRVT{suffix}', [year]).get(year, 0)
            c_gov = self.handler.get_variable(f'YEMNECONGOVT{suffix}', [year]).get(year, 0)
            invest = self.handler.get_variable(f'YEMNEGDIFTOT{suffix}', [year]).get(year, 0)
            inventory = self.handler.get_variable(f'YEMNEGDISTKB{suffix}', [year]).get(year, 0)
            exports = self.handler.get_variable(f'YEMNEEXPGNFS{suffix}', [year]).get(year, 0)
            imports = self.handler.get_variable(f'YEMNEIMPGNFS{suffix}', [year]).get(year, 0)
            discrepancy = self.handler.get_variable(f'YEMNYGDPDISC{suffix}', [year]).get(year, 0)
            
            # Calculate GDP from components
            gdp_calc = c_priv + c_gov + invest + inventory + (exports - imports) + discrepancy
            
            # Check identity with special handling for statistical discrepancy
            # For crisis economies, allow up to 3% of GDP as statistical discrepancy
            stat_disc_tolerance = 0.03  # 3% of GDP allowed as statistical discrepancy
            if gdp != 0:
                error_pct = abs((gdp - gdp_calc) / gdp) * 100
                
                # Check if statistical discrepancy is within crisis economy limits
                stat_disc_pct = abs(discrepancy / gdp) * 100 if gdp != 0 else 0
                
                # Valid if either:
                # 1. Regular tolerance is met, OR
                # 2. Statistical discrepancy is within 3% and the identity error is small
                valid = (error_pct <= self.tolerance) or (stat_disc_pct <= stat_disc_tolerance * 100 and error_pct <= 0.1)
                
                result = {
                    'identity': identity_name,
                    'year': year,
                    'gdp_reported': gdp,
                    'gdp_calculated': gdp_calc,
                    'difference': gdp - gdp_calc,
                    'error_pct': error_pct,
                    'valid': valid,
                    'components': {
                        'c_private': c_priv,
                        'c_government': c_gov,
                        'investment': invest,
                        'inventory': inventory,
                        'exports': exports,
                        'imports': imports,
                        'stat_discrepancy': discrepancy
                    }
                }
                
                self.validation_results.append(result)
                
                if not valid:
                    all_valid = False
                    logger.warning(f"{identity_name} identity violated for {year}: "
                                 f"GDP={gdp:,.0f}, Calculated={gdp_calc:,.0f}, "
                                 f"Error={error_pct:.2f}%")
        
        return all_valid
    
    def validate_gdp_production_identity(self, years: List[int], real: bool = False) -> bool:
        """
        Validate GDP = Agriculture + Industry + Services + Net Indirect Taxes
        
        Args:
            years: List of years to validate
            real: If True, check real values (KN), else nominal (CN)
            
        Returns:
            True if identity holds within tolerance for all years
        """
        suffix = 'KN' if real else 'CN'
        identity_name = f"GDP Production ({'Real' if real else 'Nominal'})"
        
        all_valid = True
        
        for year in years:
            # Get GDP at market prices
            gdp_market = self.handler.get_variable(f'YEMNYGDPMKTP{suffix}', [year]).get(year, 0)
            
            # Get sectoral components
            agriculture = self.handler.get_variable(f'YEMNVAGRTOTL{suffix}', [year]).get(year, 0)
            industry = self.handler.get_variable(f'YEMNVINDTOTL{suffix}', [year]).get(year, 0)
            services = self.handler.get_variable(f'YEMNVSRVTOTL{suffix}', [year]).get(year, 0)
            
            # Get net indirect taxes
            net_taxes = self.handler.get_variable(f'YEMNYTAXNIND{suffix}', [year]).get(year, 0)
            
            # GDP at factor cost
            gdp_fc = self.handler.get_variable(f'YEMNYGDPFCST{suffix}', [year]).get(year, 0)
            
            # Calculate GDP from production
            gdp_calc_fc = agriculture + industry + services
            gdp_calc_market = gdp_calc_fc + net_taxes
            
            # Check both identities
            if gdp_fc != 0:
                fc_error_pct = abs((gdp_fc - gdp_calc_fc) / gdp_fc) * 100
                fc_valid = fc_error_pct <= self.tolerance
                
                if not fc_valid:
                    all_valid = False
                    logger.warning(f"GDP Factor Cost identity violated for {year}: "
                                 f"GDP_FC={gdp_fc:,.0f}, Calculated={gdp_calc_fc:,.0f}, "
                                 f"Error={fc_error_pct:.2f}%")
            
            if gdp_market != 0:
                market_error_pct = abs((gdp_market - gdp_calc_market) / gdp_market) * 100
                market_valid = market_error_pct <= self.tolerance
                
                result = {
                    'identity': identity_name,
                    'year': year,
                    'gdp_market_reported': gdp_market,
                    'gdp_market_calculated': gdp_calc_market,
                    'difference': gdp_market - gdp_calc_market,
                    'error_pct': market_error_pct,
                    'valid': market_valid,
                    'components': {
                        'agriculture': agriculture,
                        'industry': industry,
                        'services': services,
                        'net_indirect_taxes': net_taxes
                    }
                }
                
                self.validation_results.append(result)
                
                if not market_valid:
                    all_valid = False
                    logger.warning(f"{identity_name} Market Price identity violated for {year}: "
                                 f"GDP={gdp_market:,.0f}, Calculated={gdp_calc_market:,.0f}, "
                                 f"Error={market_error_pct:.2f}%")
        
        return all_valid
    
    def validate_deflator_relationships(self, years: List[int]) -> bool:
        """
        Validate Nominal = Real × (Deflator/100) for all variables
        
        Returns:
            True if all deflator relationships hold within tolerance
        """
        all_valid = True
        
        # Key variable pairs to check
        variable_pairs = [
            ('YEMNYGDPMKTP', 'GDP'),
            ('YEMNECONPRVT', 'Private Consumption'),
            ('YEMNECONGOVT', 'Government Consumption'),
            ('YEMNEGDIFTOT', 'Investment'),
            ('YEMNEEXPGNFS', 'Exports'),
            ('YEMNEIMPGNFS', 'Imports'),
            ('YEMNVAGRTOTL', 'Agriculture'),
            ('YEMNVINDTOTL', 'Industry'),
            ('YEMNVSRVTOTL', 'Services')
        ]
        
        for var_base, var_name in variable_pairs:
            for year in years:
                nominal = self.handler.get_variable(f'{var_base}CN', [year]).get(year, 0)
                real = self.handler.get_variable(f'{var_base}KN', [year]).get(year, 0)
                
                if real != 0 and nominal != 0:
                    # Calculate implied deflator
                    deflator = (nominal / real) * 100
                    
                    # Recalculate nominal from real and deflator
                    nominal_calc = real * (deflator / 100)
                    
                    error_pct = abs((nominal - nominal_calc) / nominal) * 100
                    valid = error_pct <= self.tolerance
                    
                    if not valid:
                        all_valid = False
                        logger.warning(f"Deflator relationship violated for {var_name} in {year}: "
                                     f"Nominal={nominal:,.0f}, Real={real:,.0f}, "
                                     f"Deflator={deflator:.1f}, Error={error_pct:.2f}%")
                        
                        result = {
                            'identity': f"Deflator Relationship - {var_name}",
                            'year': year,
                            'nominal': nominal,
                            'real': real,
                            'deflator': deflator,
                            'nominal_recalc': nominal_calc,
                            'error_pct': error_pct,
                            'valid': valid
                        }
                        self.validation_results.append(result)
        
        return all_valid
    
    def validate_investment_decomposition(self, years: List[int]) -> bool:
        """
        Validate Total Investment = Public Investment + Private Investment
        
        Returns:
            True if identity holds for all years
        """
        all_valid = True
        
        for suffix in ['CN', 'KN']:
            identity_name = f"Investment Decomposition ({'Nominal' if suffix == 'CN' else 'Real'})"
            
            for year in years:
                total = self.handler.get_variable(f'YEMNEGDIFTOT{suffix}', [year]).get(year, 0)
                public = self.handler.get_variable(f'YEMNEGDIFGOV{suffix}', [year]).get(year, 0)
                private = self.handler.get_variable(f'YEMNEGDIFPRV{suffix}', [year]).get(year, 0)
                
                if total != 0:
                    calculated = public + private
                    error_pct = abs((total - calculated) / total) * 100
                    valid = error_pct <= self.tolerance
                    
                    if not valid:
                        all_valid = False
                        logger.warning(f"{identity_name} violated for {year}: "
                                     f"Total={total:,.0f}, Public+Private={calculated:,.0f}, "
                                     f"Error={error_pct:.2f}%")
                        
                        result = {
                            'identity': identity_name,
                            'year': year,
                            'total_investment': total,
                            'public_investment': public,
                            'private_investment': private,
                            'calculated_total': calculated,
                            'error_pct': error_pct,
                            'valid': valid
                        }
                        self.validation_results.append(result)
        
        return all_valid
    
    def validate_consumption_decomposition(self, years: List[int]) -> bool:
        """
        Validate relationships between consumption components
        Note: Total consumption = Private + Government (already checked in GDP identity)
        """
        # This is implicitly validated through GDP expenditure identity
        # Could add additional checks here if needed
        return True
    
    def validate_fiscal_identities(self, years: List[int]) -> bool:
        """
        Validate all fiscal identities:
        1. Revenue composition
        2. Expenditure composition
        3. Fiscal balance calculations
        """
        all_valid = True
        
        for year in years:
            # 1. Revenue Identity: Total = Tax + Grants + Other + Resource Revenues
            total_rev = self.handler.get_variable('YEMGGREVTOTLCN', [year]).get(year, 0)
            tax_rev = self.handler.get_variable('YEMGGREVTAXTCN', [year]).get(year, 0)
            grants = self.handler.get_variable('YEMGGREVGRNTCN', [year]).get(year, 0)
            other_rev = self.handler.get_variable('YEMGGREVOTHRCN', [year]).get(year, 0)
            resource_rev = self.handler.get_variable('YEMGGREVCOMMCN', [year]).get(year, 0)
            
            if total_rev != 0:
                # Try both with and without resource revenues
                rev_calc_simple = tax_rev + grants + other_rev
                rev_calc_with_resources = tax_rev + grants + other_rev + resource_rev
                
                # Check which decomposition matches
                error_simple = abs((total_rev - rev_calc_simple) / total_rev) * 100
                error_with_resources = abs((total_rev - rev_calc_with_resources) / total_rev) * 100
                
                # Use the decomposition that fits better
                if error_with_resources < error_simple:
                    rev_calc = rev_calc_with_resources
                    error_pct = error_with_resources
                    includes_resources = True
                else:
                    rev_calc = rev_calc_simple
                    error_pct = error_simple
                    includes_resources = False
                
                valid = error_pct <= self.tolerance
                
                if not valid:
                    all_valid = False
                    logger.warning(f"Fiscal Revenue identity violated for {year}: "
                                 f"Total={total_rev:,.0f}, Calculated={rev_calc:,.0f}, "
                                 f"Error={error_pct:.2f}%")
                    
                    self.validation_results.append({
                        'identity': 'Fiscal Revenue Composition',
                        'year': year,
                        'total_revenue': total_rev,
                        'tax_revenue': tax_rev,
                        'grants': grants,
                        'other_revenue': other_rev,
                        'resource_revenue': resource_rev,
                        'calculated_total': rev_calc,
                        'includes_resources': includes_resources,
                        'error_pct': error_pct,
                        'valid': valid
                    })
            
            # 2. Expenditure Identity: Total = Current + Capital + Other
            total_exp = self.handler.get_variable('YEMGGEXPTOTLCN', [year]).get(year, 0)
            current_exp = self.handler.get_variable('YEMGGEXPCRNTCN', [year]).get(year, 0)
            capital_exp = self.handler.get_variable('YEMGGEXPCAPTCN', [year]).get(year, 0)
            other_exp = self.handler.get_variable('YEMGGEXPOTHRCN', [year]).get(year, 0)
            
            if total_exp != 0:
                exp_calc = current_exp + capital_exp + other_exp
                error_pct = abs((total_exp - exp_calc) / total_exp) * 100
                valid = error_pct <= self.tolerance
                
                if not valid:
                    all_valid = False
                    logger.warning(f"Fiscal Expenditure identity violated for {year}: "
                                 f"Total={total_exp:,.0f}, Calculated={exp_calc:,.0f}, "
                                 f"Error={error_pct:.2f}%")
                    
                    self.validation_results.append({
                        'identity': 'Fiscal Expenditure Composition',
                        'year': year,
                        'total_expenditure': total_exp,
                        'current_expenditure': current_exp,
                        'capital_expenditure': capital_exp,
                        'other_expenditure': other_exp,
                        'calculated_total': exp_calc,
                        'error_pct': error_pct,
                        'valid': valid
                    })
            
            # 3. Current Expenditure Decomposition
            wages = self.handler.get_variable('YEMGGEXPWAGECN', [year]).get(year, 0)
            goods_services = self.handler.get_variable('YEMGGEXPGNFSCN', [year]).get(year, 0)
            interest = self.handler.get_variable('YEMGGEXPINTPCN', [year]).get(year, 0)
            transfers = self.handler.get_variable('YEMGGEXPTRNSCN', [year]).get(year, 0)
            other_current = self.handler.get_variable('YEMGGEXPCROTCN', [year]).get(year, 0)
            
            if current_exp != 0:
                current_calc = wages + goods_services + interest + transfers + other_current
                error_pct = abs((current_exp - current_calc) / current_exp) * 100
                valid = error_pct <= self.tolerance
                
                if not valid:
                    all_valid = False
                    logger.warning(f"Current Expenditure decomposition violated for {year}: "
                                 f"Total={current_exp:,.0f}, Calculated={current_calc:,.0f}, "
                                 f"Error={error_pct:.2f}%")
            
            # 4. Fiscal Balance Identities
            overall_bal = self.handler.get_variable('YEMGGBALOVRLCN', [year]).get(year, 0)
            primary_bal = self.handler.get_variable('YEMGGBALPRIMCN', [year]).get(year, 0)
            
            # Overall Balance = Revenue - Expenditure
            if total_rev != 0 and total_exp != 0:
                overall_calc = total_rev - total_exp
                error = abs(overall_bal - overall_calc)
                # Use absolute error for balances as they can be close to zero
                valid = error <= abs(overall_bal * self.tolerance / 100) if overall_bal != 0 else error < 1000
                
                if not valid:
                    all_valid = False
                    logger.warning(f"Overall Balance identity violated for {year}: "
                                 f"Reported={overall_bal:,.0f}, Calculated={overall_calc:,.0f}, "
                                 f"Error={error:,.0f}")
                    
                    self.validation_results.append({
                        'identity': 'Fiscal Overall Balance',
                        'year': year,
                        'reported_balance': overall_bal,
                        'calculated_balance': overall_calc,
                        'revenue': total_rev,
                        'expenditure': total_exp,
                        'error': error,
                        'valid': valid
                    })
            
            # Primary Balance = Overall Balance + Interest Payments
            if overall_bal != 0 and interest != 0:
                primary_calc = overall_bal + interest
                error = abs(primary_bal - primary_calc)
                valid = error <= abs(primary_bal * self.tolerance / 100) if primary_bal != 0 else error < 1000
                
                if not valid:
                    all_valid = False
                    logger.warning(f"Primary Balance identity violated for {year}: "
                                 f"Reported={primary_bal:,.0f}, Calculated={primary_calc:,.0f}, "
                                 f"Error={error:,.0f}")
        
        return all_valid
    
    def validate_external_sector_identities(self, years: List[int]) -> bool:
        """
        Validate Balance of Payments identities
        Note: May have limited data availability
        """
        all_valid = True
        
        for year in years:
            # Current Account Components
            ca_balance = self.handler.get_variable('YEMBNCABFUNDCD', [year]).get(year, 0)
            
            # Trade components
            exports_merch = self.handler.get_variable('YEMBXGSRMRCHCD', [year]).get(year, 0)
            exports_serv = self.handler.get_variable('YEMBXGSRNFSVCD', [year]).get(year, 0)
            imports_merch = self.handler.get_variable('YEMBMGSRMRCHCD', [year]).get(year, 0)
            imports_serv = self.handler.get_variable('YEMBMGSRNFSVCD', [year]).get(year, 0)
            
            # Income flows
            income_receipts = self.handler.get_variable('YEMBXFSTCABTCD', [year]).get(year, 0)
            income_payments = self.handler.get_variable('YEMBMFSTCABTCD', [year]).get(year, 0)
            
            if ca_balance != 0 and all([exports_merch, imports_merch]):
                # Calculate current account from components
                trade_balance = (exports_merch + exports_serv) - (imports_merch + imports_serv)
                income_balance = income_receipts - income_payments
                ca_calc = trade_balance + income_balance
                
                # Check if calculation matches reported value
                error = abs(ca_balance - ca_calc)
                # Current account can be negative, so use appropriate error measure
                error_pct = abs(error / ca_balance) * 100 if ca_balance != 0 else 0
                valid = error_pct <= self.tolerance  # Use standard tolerance for BOP data
                
                if not valid and error > 100:  # Only report significant errors
                    all_valid = False
                    logger.warning(f"Current Account identity check for {year}: "
                                 f"Reported={ca_balance:,.0f}, Calculated={ca_calc:,.0f}, "
                                 f"Difference={error:,.0f}")
                    
                    self.validation_results.append({
                        'identity': 'Current Account Balance',
                        'year': year,
                        'reported_ca': ca_balance,
                        'calculated_ca': ca_calc,
                        'trade_balance': trade_balance,
                        'income_balance': income_balance,
                        'error': error,
                        'valid': valid
                    })
        
        return all_valid
    
    def validate_gdp_fiscal_consistency(self, years: List[int]) -> bool:
        """
        Validate consistency between fiscal accounts and GDP accounts
        Specifically: Government consumption in GDP vs fiscal expenditure (correct methodology)
        """
        all_valid = True
        
        for year in years:
            # Government consumption from GDP accounts
            gov_cons_gdp = self.handler.get_variable('YEMNECONGOVTCN', [year]).get(year, 0)
            
            # Correct methodology: Government consumption = Wages + Goods & Services
            wages = self.handler.get_variable('YEMGGEXPWAGECN', [year]).get(year, 0)
            goods_services = self.handler.get_variable('YEMGGEXPGNFSCN', [year]).get(year, 0)
            
            if gov_cons_gdp != 0 and (wages != 0 or goods_services != 0):
                # Calculate expected government consumption from fiscal accounts
                gov_cons_fiscal = wages + goods_services
                
                # Check consistency (allow small deviations)
                error_pct = abs((gov_cons_gdp - gov_cons_fiscal) / gov_cons_gdp) * 100
                valid = error_pct <= 5.0  # Allow up to 5% deviation
                
                if not valid:
                    all_valid = False
                    logger.warning(f"GDP-Fiscal consistency check for {year}: "
                                 f"Gov Consumption GDP={gov_cons_gdp:,.0f}, "
                                 f"Wages + Goods&Services={gov_cons_fiscal:,.0f}, "
                                 f"Error={error_pct:.2f}%")
                    
                    self.validation_results.append({
                        'identity': 'GDP-Fiscal Consistency',
                        'year': year,
                        'gov_consumption_gdp': gov_cons_gdp,
                        'current_exp_minus_interest': gov_cons_fiscal,
                        'error_pct': error_pct,
                        'valid': valid
                    })
        
        return all_valid
    
    def get_validation_summary(self) -> str:
        """Generate a summary report of all validation results"""
        if not self.validation_results:
            return "No validation results available. Run validate_all() first."
        
        report = []
        report.append("=" * 80)
        report.append("IDENTITY VALIDATION SUMMARY")
        report.append("=" * 80)
        
        # Group by identity type
        identity_groups = {}
        for result in self.validation_results:
            identity = result['identity']
            if identity not in identity_groups:
                identity_groups[identity] = []
            identity_groups[identity].append(result)
        
        # Report each identity
        total_checks = 0
        total_valid = 0
        
        for identity, results in identity_groups.items():
            report.append(f"\n{identity}:")
            report.append("-" * len(identity))
            
            valid_count = sum(1 for r in results if r['valid'])
            total_count = len(results)
            total_checks += total_count
            total_valid += valid_count
            
            report.append(f"Passed: {valid_count}/{total_count} ({valid_count/total_count*100:.1f}%)")
            
            # Show failures
            failures = [r for r in results if not r['valid']]
            if failures:
                report.append("\nFailures:")
                for fail in failures:
                    report.append(f"  Year {fail['year']}: Error = {fail['error_pct']:.2f}%")
        
        # Overall summary
        report.append("\n" + "=" * 80)
        report.append(f"OVERALL: {total_valid}/{total_checks} checks passed "
                     f"({total_valid/total_checks*100:.1f}%)")
        report.append("=" * 80)
        
        return "\n".join(report)
    
    def validate_current_account_components(self, years: List[int]) -> bool:
        """
        Validate Current Account = Trade Balance + Income Flows
        Note: This requires BOP data which may not be complete
        """
        # Implementation depends on available BOP variables
        # For now, return True as this is a secondary check
        return True
    
    def validate_bop_identity(self, years: List[int]) -> bool:
        """
        Validate Balance of Payments identity (CORRECTED FORMULA):
        Current Account + Capital Account + Financial Account + Errors & Omissions = 0

        This is the standard BOP accounting identity. E&O is the balancing item that
        should automatically adjust to close any financing gaps.

        Args:
            years: List of years to validate

        Returns:
            True if BOP sums to zero within tolerance (after E&O adjustment)
        """
        all_valid = True

        for year in years:
            # Get BOP components
            current_account = self.handler.get_variable('YEMBNCABFUNDCD', [year]).get(year, 0)
            capital_account = self.handler.get_variable('YEMBFCAFCAPTCD', [year]).get(year, 0)
            financial_account = self.handler.get_variable('YEMBFCAFFINXCD', [year]).get(year, 0)
            reserve_changes = self.handler.get_variable('YEMBFCAFRACGCD', [year]).get(year, 0)
            errors_omissions = self.handler.get_variable('YEMBFCAFNEOMCD', [year]).get(year, 0)

            # CORRECTED BOP FORMULA: CA + KA + FA + E&O = 0
            # Note: Reserve changes are included in Financial Account in standard BOP
            financing_gap = -(current_account + capital_account + financial_account)

            # Auto-adjust E&O to close financing gaps (as user requested)
            if abs(financing_gap) > 10:  # Only adjust if gap > $10 million
                logger.info(f"Auto-adjusting E&O for {year}: {errors_omissions:,.0f} → {financing_gap:,.0f}")
                # Update E&O in the data to close the gap
                self.handler.update_variable('YEMBFCAFNEOMCD', year, financing_gap)
                errors_omissions = financing_gap

            # Calculate BOP sum with corrected formula
            bop_sum = current_account + capital_account + financial_account + errors_omissions
            
            # Check if sum is close to zero (should be after E&O adjustment)
            tolerance_usd = 5  # $5 million tolerance after E&O adjustment
            valid = abs(bop_sum) <= tolerance_usd

            if not valid:
                all_valid = False
                logger.warning(f"BOP identity violated for {year} after E&O adjustment: "
                             f"CA+KA+FA+E&O = {bop_sum:,.0f}, should be 0")
            else:
                logger.info(f"BOP identity satisfied for {year}: "
                           f"CA+KA+FA+E&O = {bop_sum:,.0f}")

            self.validation_results.append({
                'identity': 'Balance of Payments',
                'year': year,
                'bop_sum': bop_sum,
                'financing_gap_closed': financing_gap,
                'current_account': current_account,
                'capital_account': capital_account,
                'financial_account': financial_account,
                'reserve_changes': reserve_changes,
                'errors_omissions': errors_omissions,
                'valid': valid
            })

        return all_valid
    
    def validate_savings_investment_identity(self, years: List[int]) -> bool:
        """
        Validate Savings-Investment identity using correct national accounting approach:
        
        From GDP identity: GDP = C + I + G + (X - M) + ΔInv
        We derive: I + ΔInv = GDP - C - G - (X - M)
        
        Where the right side represents total savings (domestic + foreign).
        Foreign savings = -(X - M) = -Net Exports
        Therefore: I + ΔInv = Domestic Savings + Foreign Savings
        
        This validation checks that investment equals available savings from both 
        domestic and foreign sources.
        """
        all_valid = True

        for year in years:
            # Get investment components
            investment_fixed = self.handler.get_variable('YEMNEGDIFTOTCN', [year]).get(year, 0)
            inventory_change = self.handler.get_variable('YEMNEGDISTKBCN', [year]).get(year, 0)
            investment_total = investment_fixed + inventory_change

            # Get GDP and consumption components  
            gdp_nominal = self.handler.get_variable('YEMNYGDPMKTPCN', [year]).get(year, 0)
            c_private = self.handler.get_variable('YEMNECONPRVTCN', [year]).get(year, 0)
            c_government = self.handler.get_variable('YEMNECONGOVTCN', [year]).get(year, 0)

            # Get trade balance for net exports
            exports = self.handler.get_variable('YEMNEEXPGNFSCN', [year]).get(year, 0)
            imports = self.handler.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)
            net_exports = exports - imports

            if gdp_nominal != 0 and investment_total != 0:
                # Calculate total available savings from GDP identity
                # GDP = C + I + G + (X - M) + ΔInv
                # Rearranging: I + ΔInv = GDP - C - G - (X - M)
                total_available_savings = gdp_nominal - c_private - c_government - net_exports

                # Check the savings-investment identity
                error = abs(investment_total - total_available_savings)
                error_pct = (error / abs(investment_total)) * 100

                # For crisis economies, accept large S-I gaps as normal
                valid = error_pct <= 5  # 5% tolerance for crisis economy

                if not valid:
                    all_valid = False
                    logger.warning(f"S-I identity gap for {year}: "
                                 f"I+ΔInv={investment_total:,.0f}, "
                                 f"Available_Savings={total_available_savings:,.0f}, "
                                 f"Gap={error_pct:.1f}% (exceeds 5% tolerance)")
                else:
                    logger.info(f"S-I identity for {year}: {error_pct:.1f}% gap (within tolerance)")

                # For debugging, calculate the implied domestic and foreign savings
                # Foreign savings = -Net Exports (by definition)
                foreign_savings_implied = -net_exports
                domestic_savings_implied = total_available_savings - foreign_savings_implied
                
                # Get actual current account for comparison
                current_account = self.handler.get_variable('YEMBNCABFUNDCD', [year]).get(year, 0)
                exchange_rate = self.handler.get_variable('YEMPANUSATLS', [year]).get(year, 1)
                foreign_savings_from_bop = -current_account * exchange_rate if (current_account != 0 and exchange_rate != 0) else 0

                # Add detailed debug info
                self.validation_results.append({
                    'identity': 'Savings-Investment',
                    'year': year,
                    'investment_fixed': investment_fixed,
                    'inventory_change': inventory_change,
                    'investment_total': investment_total,
                    'gdp_nominal': gdp_nominal,
                    'c_private': c_private,
                    'c_government': c_government,
                    'net_exports': net_exports,
                    'total_available_savings': total_available_savings,
                    'domestic_savings_implied': domestic_savings_implied,
                    'foreign_savings_implied': foreign_savings_implied,
                    'foreign_savings_from_bop': foreign_savings_from_bop,
                    'current_account_usd': current_account,
                    'exchange_rate': exchange_rate,
                    'error_pct': error_pct,
                    'valid': valid
                })
        
        return all_valid
    
    def validate_cross_sector_trade_consistency(self, years: List[int]) -> bool:
        """
        Validate consistency between National Accounts and BOP trade data
        Exports_NA (LCU) ≈ (Exports_goods_BOP + Exports_services_BOP) × Exchange_Rate
        Imports_NA (LCU) ≈ (Imports_goods_BOP + Imports_services_BOP) × Exchange_Rate
        """
        all_valid = True
        
        for year in years:
            # Get exchange rate
            exchange_rate = self.handler.get_variable('YEMPANUSATLS', [year]).get(year, 0)
            
            if exchange_rate == 0:
                continue
            
            # National Accounts trade (in millions LCU)
            exports_na = self.handler.get_variable('YEMNEEXPGNFSCN', [year]).get(year, 0)
            imports_na = self.handler.get_variable('YEMNEIMPGNFSCN', [year]).get(year, 0)
            
            # BOP trade (in USD millions)
            exports_goods_bop = self.handler.get_variable('YEMBXGSRMRCHCD', [year]).get(year, 0)
            exports_services_bop = self.handler.get_variable('YEMBXGSRNFSVCD', [year]).get(year, 0)
            imports_goods_bop = self.handler.get_variable('YEMBMGSRMRCHCD', [year]).get(year, 0)
            imports_services_bop = self.handler.get_variable('YEMBMGSRNFSVCD', [year]).get(year, 0)
            
            # Convert BOP to LCU
            # BOP data is in millions USD, NA data is in millions LCU
            # millions USD * exchange_rate = millions LCU
            exports_bop_lcu = (exports_goods_bop + exports_services_bop) * exchange_rate
            imports_bop_lcu = (imports_goods_bop + imports_services_bop) * exchange_rate
            
            # Check consistency
            if exports_na != 0:
                export_ratio = exports_bop_lcu / exports_na
                export_valid = 0.95 <= export_ratio <= 1.05  # Within 5%
                
                if not export_valid:
                    all_valid = False
                    logger.info(f"Export consistency check for {year}: "
                               f"NA={exports_na:,.0f}, BOP={exports_bop_lcu:,.0f}, "
                               f"Ratio={export_ratio:.2f}")
                    
                    # Add detailed validation result
                    self.validation_results.append({
                        'identity': 'Trade Consistency - Exports',
                        'year': year,
                        'exports_na_millions_lcu': exports_na,
                        'exports_bop_millions_usd': exports_goods_bop + exports_services_bop,
                        'exports_bop_millions_lcu': exports_bop_lcu,
                        'exchange_rate': exchange_rate,
                        'ratio': export_ratio,
                        'valid': export_valid
                    })
            
            if imports_na != 0:
                import_ratio = imports_bop_lcu / imports_na
                import_valid = 0.95 <= import_ratio <= 1.05  # Within 5%
                
                if not import_valid:
                    all_valid = False
                    logger.info(f"Import consistency check for {year}: "
                               f"NA={imports_na:,.0f}, BOP={imports_bop_lcu:,.0f}, "
                               f"Ratio={import_ratio:.2f}")
                    
                    # Add detailed validation result
                    self.validation_results.append({
                        'identity': 'Trade Consistency - Imports',
                        'year': year,
                        'imports_na_millions_lcu': imports_na,
                        'imports_bop_millions_usd': imports_goods_bop + imports_services_bop,
                        'imports_bop_millions_lcu': imports_bop_lcu,
                        'exchange_rate': exchange_rate,
                        'ratio': import_ratio,
                        'valid': import_valid
                    })
        
        return all_valid