"""
Validate alignment results against IMF targets
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple
import logging

logger = logging.getLogger(__name__)


class AlignmentValidator:
    """Validate WB-IMF alignment results"""
    
    def __init__(self, data_handler, target_processor):
        """Initialize validator
        
        Args:
            data_handler: YemenDataHandler instance
            target_processor: IMFTargetProcessor instance
        """
        self.data = data_handler
        self.targets = target_processor
        
    def validate_gdp(self, years: List[int]) -> pd.DataFrame:
        """Validate GDP alignment
        
        Args:
            years: Years to validate
            
        Returns:
            DataFrame with validation results
        """
        results = []
        gdp_targets = self.targets.get_gdp_targets(years)
        
        for year in years:
            # Get current values
            gdp_nominal_lcu = self.data.get_variable('YEMNYGDPMKTPCN', [year])[year]
            exchange_rate = self.data.get_variable('YEMPANUSATLS', [year])[year]
            
            if pd.notna(gdp_nominal_lcu) and pd.notna(exchange_rate):
                # Convert to USD billions
                gdp_usd = gdp_nominal_lcu / exchange_rate / 1000
                target = gdp_targets[year]
                
                results.append({
                    'Year': year,
                    'Indicator': 'GDP Nominal (USD billions)',
                    'WB Value': round(gdp_usd, 1),
                    'IMF Target': target,
                    'Gap': round(gdp_usd - target, 1),
                    'Gap %': round((gdp_usd - target) / target * 100, 1) if target != 0 else 0,
                    'Status': '✓' if abs(gdp_usd - target) < 0.1 else '✗'
                })
                
        return pd.DataFrame(results)
    
    def validate_fiscal(self, years: List[int]) -> pd.DataFrame:
        """Validate fiscal indicators
        
        Args:
            years: Years to validate
            
        Returns:
            DataFrame with validation results
        """
        results = []
        fiscal_targets = self.targets.get_fiscal_targets(years)
        
        for year in years:
            # Revenue
            if fiscal_targets['revenue'][year] is not None:  # None means preserve WB value
                revenue_pct = self.data.calculate_fiscal_percent_gdp('YEMGGREVTOTLCN', year)
                target_rev = fiscal_targets['revenue'][year]
                
                results.append({
                    'Year': year,
                    'Indicator': 'Revenue (% of GDP)',
                    'WB Value': round(revenue_pct, 1),
                    'IMF Target': target_rev,
                    'Gap': round(revenue_pct - target_rev, 1),
                    'Gap %': round((revenue_pct - target_rev) / target_rev * 100, 1) if target_rev != 0 else 0,
                    'Status': '✓' if abs(revenue_pct - target_rev) < 0.2 else '✗'
                })
            
            # Expenditure
            exp_pct = self.data.calculate_fiscal_percent_gdp('YEMGGEXPTOTLCN', year)
            target_exp = fiscal_targets['expenditure'][year]
            
            results.append({
                'Year': year,
                'Indicator': 'Expenditure (% of GDP)',
                'WB Value': round(exp_pct, 1),
                'IMF Target': target_exp,
                'Gap': round(exp_pct - target_exp, 1),
                'Gap %': round((exp_pct - target_exp) / target_exp * 100, 1) if target_exp != 0 else 0,
                'Status': '✓' if abs(exp_pct - target_exp) < 0.2 else '✗'
            })
            
        return pd.DataFrame(results)
    
    def validate_trade(self, years: List[int]) -> pd.DataFrame:
        """Validate trade indicators
        
        Args:
            years: Years to validate
            
        Returns:
            DataFrame with validation results
        """
        results = []
        trade_targets = self.targets.get_trade_targets(years)
        
        for year in years:
            # Imports
            imports_lcu = self.data.get_variable('YEMNEIMPGNFSCN', [year])[year]
            exchange_rate = self.data.get_variable('YEMPANUSATLS', [year])[year]
            
            if pd.notna(imports_lcu) and pd.notna(exchange_rate):
                imports_usd = imports_lcu / exchange_rate / 1000  # billions
                target = trade_targets['imports'][year]
                
                results.append({
                    'Year': year,
                    'Indicator': 'Imports (USD billions)',
                    'WB Value': round(imports_usd, 1),
                    'IMF Target': target,
                    'Gap': round(imports_usd - target, 1),
                    'Gap %': round((imports_usd - target) / target * 100, 1) if target != 0 else 0,
                    'Status': '✓' if abs(imports_usd - target) < 0.1 else '✗'
                })
                
        return pd.DataFrame(results)
    
    def validate_inflation(self, years: List[int]) -> pd.DataFrame:
        """Validate inflation targets
        
        Args:
            years: Years to validate
            
        Returns:
            DataFrame with validation results
        """
        results = []
        inflation_targets = self.targets.get_inflation_targets(years)
        
        for year in years:
            if year > min(years):  # Need previous year for inflation
                cpi_current = self.data.get_variable('YEMFPCPITOTLXN', [year])[year]
                cpi_prev = self.data.get_variable('YEMFPCPITOTLXN', [year-1])[year-1]
                
                if pd.notna(cpi_current) and pd.notna(cpi_prev) and cpi_prev != 0:
                    inflation = ((cpi_current - cpi_prev) / cpi_prev) * 100
                    target = inflation_targets[year]
                    
                    results.append({
                        'Year': year,
                        'Indicator': 'CPI Inflation (%)',
                        'WB Value': round(inflation, 1),
                        'IMF Target': target,
                        'Gap': round(inflation - target, 1),
                        'Gap %': round((inflation - target) / target * 100, 1) if target != 0 else 0,
                        'Status': '✓' if abs(inflation - target) < 0.5 else '✗'
                    })
                    
        return pd.DataFrame(results)
    
    def validate_consistency(self) -> Dict[str, bool]:
        """Validate mathematical consistency of the data
        
        Returns:
            Dict of consistency checks and results
        """
        checks = {}
        
        # Check: Nominal = Real × Deflator / 100
        years = [2022, 2023, 2024, 2025]
        gdp_consistent = True
        
        for year in years:
            gdp_real = self.data.get_variable('YEMNYGDPMKTPKN', [year])[year]
            gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year])[year]
            
            if pd.notna(gdp_real) and pd.notna(gdp_nominal) and gdp_real != 0:
                deflator = (gdp_nominal / gdp_real) * 100
                recalc_nominal = gdp_real * deflator / 100
                
                # Check if they match within tolerance
                if abs(recalc_nominal - gdp_nominal) > 0.1:
                    gdp_consistent = False
                    logger.warning(f"GDP consistency issue in {year}: {gdp_nominal} vs {recalc_nominal}")
        
        checks['gdp_consistency'] = gdp_consistent
        
        # Add other consistency checks...
        
        return checks
    
    def generate_validation_report(self, years: List[int]) -> str:
        """Generate comprehensive validation report
        
        Args:
            years: Years to validate
            
        Returns:
            Markdown formatted report
        """
        report = ["# WB-IMF Alignment Validation Report\n"]
        report.append(f"Generated: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        # GDP validation
        report.append("## GDP Validation\n")
        gdp_df = self.validate_gdp(years)
        if not gdp_df.empty:
            report.append(gdp_df.to_markdown(index=False))
        
        # Fiscal validation
        report.append("\n## Fiscal Indicators Validation\n")
        fiscal_df = self.validate_fiscal(years)
        if not fiscal_df.empty:
            report.append(fiscal_df.to_markdown(index=False))
        
        # Trade validation
        report.append("\n## Trade Indicators Validation\n")
        trade_df = self.validate_trade(years)
        if not trade_df.empty:
            report.append(trade_df.to_markdown(index=False))
        
        # Inflation validation
        report.append("\n## Inflation Validation\n")
        inflation_df = self.validate_inflation(years)
        if not inflation_df.empty:
            report.append(inflation_df.to_markdown(index=False))
        
        # Consistency checks
        report.append("\n## Consistency Checks\n")
        consistency = self.validate_consistency()
        for check, result in consistency.items():
            status = "✓ PASS" if result else "✗ FAIL"
            report.append(f"- {check}: {status}")
        
        # Summary
        report.append("\n## Summary\n")
        all_dfs = [gdp_df, fiscal_df, trade_df, inflation_df]
        total_checks = sum(len(df) for df in all_dfs if not df.empty)
        passed_checks = sum((df['Status'] == '✓').sum() for df in all_dfs if not df.empty)
        
        report.append(f"- Total indicators checked: {total_checks}")
        report.append(f"- Passed: {passed_checks}")
        report.append(f"- Failed: {total_checks - passed_checks}")
        report.append(f"- Success rate: {passed_checks/total_checks*100:.1f}%")
        
        return "\n".join(report)