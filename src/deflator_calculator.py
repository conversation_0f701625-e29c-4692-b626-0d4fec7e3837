"""
Calculate deflator adjustments to achieve IMF targets
"""

import pandas as pd
import numpy as np
from typing import Dict, Tuple
import logging

logger = logging.getLogger(__name__)


class DeflatorCalculator:
    """Calculate required deflator adjustments"""
    
    def __init__(self, data_handler):
        """Initialize calculator
        
        Args:
            data_handler: YemenDataHandler instance
        """
        self.data = data_handler
        
    def calculate_gdp_deflator_adjustment(self, year: int, target_gdp_usd_billions: float, 
                                        exchange_rate: float) -> Tuple[float, float]:
        """Calculate required GDP deflator to achieve target GDP in USD
        
        Args:
            year: Year to calculate
            target_gdp_usd_billions: Target GDP in USD billions
            exchange_rate: Exchange rate LCU/USD
            
        Returns:
            Tuple of (new_deflator, percent_change)
        """
        # Get real GDP
        gdp_real = self.data.get_variable('YEMNYGDPMKTPKN', [year])[year]
        
        if pd.isna(gdp_real) or gdp_real == 0:
            logger.error(f"Invalid real GDP for {year}")
            return (np.nan, np.nan)
        
        # Calculate required nominal GDP in LCU millions
        # target is in billions USD, need to convert to millions LCU
        # billions USD * 1000 = millions USD
        # millions USD * exchange_rate = millions LCU
        target_gdp_lcu = target_gdp_usd_billions * 1000 * exchange_rate
        
        # Calculate required deflator
        required_deflator = (target_gdp_lcu / gdp_real) * 100
        
        # Get current deflator
        current_deflator = self.data.get_gdp_deflator(year)
        
        # Calculate percent change
        percent_change = ((required_deflator - current_deflator) / current_deflator * 100) if current_deflator != 0 else 0
        
        logger.info(f"GDP Deflator {year}: Current={current_deflator:.1f}, Required={required_deflator:.1f}, Change={percent_change:.1f}%")
        
        return (required_deflator, percent_change)
    
    def calculate_import_deflator_adjustment(self, year: int, target_imports_usd_billions: float,
                                           exchange_rate: float) -> Tuple[float, float]:
        """Calculate required import deflator adjustment
        
        Args:
            year: Year to calculate
            target_imports_usd_billions: Target imports in USD billions
            exchange_rate: Exchange rate LCU/USD
            
        Returns:
            Tuple of (adjustment_factor, new_nominal_value)
        """
        # Get real imports
        imports_real = self.data.get_variable('YEMNEIMPGNFSKN', [year])[year]
        imports_nominal = self.data.get_variable('YEMNEIMPGNFSCN', [year])[year]
        
        if pd.isna(imports_real) or imports_real == 0:
            logger.error(f"Invalid real imports for {year}")
            return (1.0, imports_nominal)
        
        # Calculate current deflator
        current_deflator = (imports_nominal / imports_real) * 100 if imports_real != 0 else 100
        
        # Calculate required nominal imports in LCU millions
        target_imports_lcu = target_imports_usd_billions * 1000 * exchange_rate
        
        # Calculate required deflator
        required_deflator = (target_imports_lcu / imports_real) * 100
        
        # Calculate adjustment factor
        adjustment_factor = required_deflator / current_deflator if current_deflator != 0 else 1.0
        
        logger.info(f"Import Deflator {year}: Current={current_deflator:.1f}, Required={required_deflator:.1f}, Factor={adjustment_factor:.3f}")
        
        return (adjustment_factor, target_imports_lcu)
    
    def apply_deflator_adjustment(self, var_code_real: str, var_code_nominal: str, 
                                year: int, adjustment_percent: float) -> float:
        """Apply deflator adjustment to calculate new nominal value
        
        Args:
            var_code_real: Real variable code
            var_code_nominal: Nominal variable code
            year: Year to adjust
            adjustment_percent: Percent change in deflator
            
        Returns:
            New nominal value
        """
        # Get real value
        real_value = self.data.get_variable(var_code_real, [year])[year]
        current_nominal = self.data.get_variable(var_code_nominal, [year])[year]
        
        if pd.isna(real_value) or real_value == 0:
            return current_nominal
        
        # Calculate current deflator
        current_deflator = (current_nominal / real_value) * 100 if real_value != 0 else 100
        
        # Apply adjustment
        new_deflator = current_deflator * (1 + adjustment_percent / 100)
        
        # Calculate new nominal
        new_nominal = real_value * new_deflator / 100
        
        return new_nominal
    
    def calculate_fiscal_adjustment(self, var_code: str, year: int, 
                                  target_percent_gdp: float) -> float:
        """Calculate fiscal variable adjustment to achieve target % of GDP
        
        Args:
            var_code: Fiscal variable code
            year: Year to adjust
            target_percent_gdp: Target as percent of GDP
            
        Returns:
            New nominal value for fiscal variable
        """
        # Get adjusted GDP (assuming it's already been calculated)
        gdp_nominal = self.data.get_variable('YEMNYGDPMKTPCN', [year])[year]
        
        # Calculate required fiscal value
        required_value = gdp_nominal * target_percent_gdp / 100
        
        current_value = self.data.get_variable(var_code, [year])[year]
        
        logger.info(f"Fiscal adjustment {var_code}[{year}]: Current={current_value:.0f}, Required={required_value:.0f}")
        
        return required_value
    
    def calculate_cpi_adjustment(self, year: int, target_inflation: float) -> float:
        """Calculate CPI adjustment to achieve target inflation
        
        Args:
            year: Year to adjust (inflation from year-1 to year)
            target_inflation: Target inflation rate (%)
            
        Returns:
            New CPI index value
        """
        # Get previous year CPI
        prev_year = year - 1
        cpi_prev = self.data.get_variable('YEMFPCPITOTLXN', [prev_year])[prev_year]
        
        if pd.isna(cpi_prev):
            logger.error(f"Cannot calculate CPI adjustment - no data for {prev_year}")
            return np.nan
        
        # Calculate required CPI
        required_cpi = cpi_prev * (1 + target_inflation / 100)
        
        current_cpi = self.data.get_variable('YEMFPCPITOTLXN', [year])[year]
        
        logger.info(f"CPI adjustment {year}: Current={current_cpi:.1f}, Required={required_cpi:.1f}")
        
        return required_cpi